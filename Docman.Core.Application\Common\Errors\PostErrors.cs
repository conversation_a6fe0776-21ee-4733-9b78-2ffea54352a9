namespace Docman.Core.Application.Common.Errors;

public static class PostErrors
{
    public static readonly Error NotFound = new(
        "Post.NotFound", 
        "The post with the specified ID was not found");

    public static readonly Error AlreadyDeleted = new(
        "Post.AlreadyDeleted", 
        "The post has already been deleted");

    public static readonly Error UnauthorizedAccess = new(
        "Post.UnauthorizedAccess", 
        "You are not authorized to access this post");

    public static readonly Error InvalidTitle = new(
        "Post.InvalidTitle", 
        "Post title cannot be empty and must not exceed 200 characters");

    public static readonly Error InvalidContent = new(
        "Post.InvalidContent", 
        "Post content cannot be empty and must not exceed 10,000 characters");

    public static readonly Error InvalidUserId = new(
        "Post.InvalidUserId", 
        "User ID is required and cannot be empty");

    public static Error NotFoundWithId(string id) => new(
        "Post.NotFound", 
        $"Post with ID '{id}' was not found");

    public static Error CreationFailed(string reason) => new(
        "Post.CreationFailed", 
        $"Failed to create post: {reason}");

    public static Error UpdateFailed(string reason) => new(
        "Post.UpdateFailed", 
        $"Failed to update post: {reason}");
}
