namespace Docman.Core.Application.Common.Errors;

public static class ValidationErrors
{
    public static readonly Error RequiredField = new(
        "Validation.Requiredield", 
        "This field is required.");
    
    public static readonly Error InvalidLength = new(
        "Validation.InvalidLength", 
        "Field length is invalid.");
    
    public static readonly Error InvalidFormat = new(
        "Validation.InvalidFormat", 
        "Field format is invalid.");
    
    public static Error CustomValidation(string message) => new(
        "Validation.CustomValidation", 
        message);
}
