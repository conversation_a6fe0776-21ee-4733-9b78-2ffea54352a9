using System.Linq.Expressions;
using Docman.Core.Domain.Common;

namespace Docman.Core.Application.Contracts;

public interface IGenericRepository<T> where T : BaseEntity
{
    // Query methods
    Task<List<T>> GetAllAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default);

    Task<T?> GetAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default);

    Task<IQueryable<T>> GetForPaginationAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false);

    Task<bool> AnyAsync(
        Expression<Func<T, bool>> filter, 
        CancellationToken cancellationToken = default);

    // Command methods
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    void Update(T entity);
    void UpdateRange(IEnumerable<T> entities);
    void Delete(T entity);
    void DeleteRange(IEnumerable<T> entities);

    // Basic CRUD methods
    Task<T> GetByIdAsync(string id, CancellationToken cancellationToken = default);
    Task<T?> GetByIdOrDefaultAsync(string id, CancellationToken cancellationToken = default);
}
