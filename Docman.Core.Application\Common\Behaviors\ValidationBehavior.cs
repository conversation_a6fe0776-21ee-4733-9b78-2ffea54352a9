﻿using FluentValidation.Results;
using System.Collections.Concurrent;
using Docman.Core.Application.Common.Errors;
using Docman.Core.Application.Common.Results;

namespace Docman.Core.Application.Common.Behaviors;

/// <summary>
/// MediatR pipeline behavior that runs all FluentValidation validators for the request.
/// Produces Result/Result&lt;T&gt; failures with structured validation data.
/// </summary>
public sealed class ValidationBehavior<TRequest, TResponse>(
    IEnumerable<IValidator<TRequest>> validatorsEnumerable
) : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    // Materialize once to avoid repeated enumeration
    private readonly IValidator<TRequest>[] _validators = validatorsEnumerable.ToArray();

    // Cache compiled delegates for Result.Failure<T>
    private static readonly ConcurrentDictionary<Type, Func<Error, object>> _failureFactories = new();

    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken)
    {
        // No validators -> skip
        if (_validators.Length == 0)
            return await next().ConfigureAwait(false);

        var failures = await ValidateRequestAsync(request, cancellationToken).ConfigureAwait(false);

        if (failures.Count == 0)
            return await next().ConfigureAwait(false);

        return CreateValidationFailureResult(failures);
    }

    /// <summary>
    /// Runs all validators asynchronously and aggregates failures.
    /// </summary>
    private async Task<IReadOnlyList<ValidationFailure>> ValidateRequestAsync(
        TRequest request,
        CancellationToken cancellationToken)
    {
        var context = new ValidationContext<TRequest>(request);

        // Use validators' native async API. No Task.Run.
        var tasks = _validators.Select(v => v.ValidateAsync(context, cancellationToken));
        var results = await Task.WhenAll(tasks).ConfigureAwait(false);

        // Avoid nulls and empty allocations
        if (results.Length == 0) 
            return [];

        var failures = results
            .SelectMany(r => r.Errors)
            .Where(f => f is not null)
            .ToArray();

        return failures.Length == 0 ? [] : failures;
    }

    /// <summary>
    /// Create appropriate Result/Result&lt;T&gt; failure from validation failures.
    /// </summary>
    private TResponse CreateValidationFailureResult(IReadOnlyList<ValidationFailure> failures)
    {
        var error = CreateValidationError(failures);

        var responseType = typeof(TResponse);

        // Handle Result (non-generic)
        if (responseType == typeof(Result))
            return (TResponse)(object)Result.Failure(error);

        // Handle Result<T>
        if (IsGenericResult(responseType))
            return CreateGenericResultFailure(responseType, error);

        // Enforce contract: handlers using this behavior should return Result types
        throw new InvalidOperationException(
            $"Handler for {typeof(TRequest).Name} must return a Result or Result<T> " +
            $"to be compatible with {nameof(ValidationBehavior<TRequest, TResponse>)}.");
    }

    /// <summary>
    /// Builds a structured validation error, grouped by property.
    /// </summary>
    private static Error CreateValidationError(IReadOnlyList<ValidationFailure> failures)
    {
        if (failures.Count == 1)
        {
            var f = failures[0];
            return new Error($"Validation.{f.PropertyName}", f.ErrorMessage);
        }

        // group messages per property for client-side usability
        var grouped = failures
            .GroupBy(f => f.PropertyName)
            .ToDictionary(
                g => g.Key,
                g => g.Where(f => !string.IsNullOrWhiteSpace(f.ErrorMessage))
                      .Select(f => f.ErrorMessage)
                      .Distinct()
                      .ToArray()
            );

        // If your Error type supports metadata, attach dictionary; otherwise join as string
        // Prefer a dedicated factory:
        // e.g., ValidationErrors.FromDictionary(grouped)
        var flattened = string.Join("; ",
            grouped.Select(kvp => $"{kvp.Key}: {string.Join(", ", kvp.Value)}"));

        return ValidationErrors.CustomValidation(flattened);
    }

    private static bool IsGenericResult(Type type) =>
        type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Result<>);

    /// <summary>
    /// Uses a cached compiled delegate to call Result.Failure&lt;T&gt;(Error) without reflection cost on each call.
    /// </summary>
    private static TResponse CreateGenericResultFailure(Type responseType, Error error)
    {
        var valueType = responseType.GetGenericArguments()[0];

        var factory = _failureFactories.GetOrAdd(valueType, static vt =>
        {
            // Build a strongly-typed lambda: (Error e) => Result.Failure<vt>(e)
            var errorParam = System.Linq.Expressions.Expression.Parameter(typeof(Error), "e");

            var genericMethod = typeof(Result)
                .GetMethod(nameof(Result.Failure), [typeof(Error)])!
                .MakeGenericMethod(vt);

            var call = System.Linq.Expressions.Expression.Call(genericMethod, errorParam);
            var cast = System.Linq.Expressions.Expression.Convert(call, typeof(object));

            var lambda = System.Linq.Expressions.Expression.Lambda<Func<Error, object>>(cast, errorParam);
            return lambda.Compile();
        });

        return (TResponse)factory(error);
    }
}
