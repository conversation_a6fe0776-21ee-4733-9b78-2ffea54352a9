using Docman.Core.Domain.Entities;
using Docman.Core.Application.Features.Posts.Commands.CreatePost;
using Docman.Core.Application.Features.Posts.Queries.GetPostById;
using Docman.Core.Application.Common.DTOs;

namespace Docman.Core.Application.Common.Mappings;

public class PostMappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // Only configure mappings that have business logic
        // All other mappings are handled by convention
        
        // CreatePostCommand to Post mapping - has business logic for ID generation and timestamps
        config.NewConfig<CreatePostCommand, Post>()
            .Map(dest => dest.Id, src => Guid.NewGuid().ToString())
            .Map(dest => dest.CreatedAt, src => DateTimeOffset.UtcNow)
            .Map(dest => dest.PublishedAt, src => src.IsPublished ? DateTimeOffset.UtcNow : (DateTimeOffset?)null)
            .Map(dest => dest.UpdatedAt, src => (DateTimeOffset?)null)
            .Map(dest => dest.IsDeleted, src => false)
            .Map(dest => dest.DeletedAt, src => (DateTimeOffset?)null);

        // Post to GetPostByIdResponse mapping - wraps PostDto
        config.NewConfig<Post, GetPostByIdResponse>()
            .Map(dest => dest.Post, src => src.Adapt<PostDto>());
    }
}
