using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Posts.Commands.DeletePost;

public sealed class DeletePostCommandHandler(
    IGenericRepository<Post> postRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<DeletePostCommand, Result>
{
    public async Task<Result> Handle(
        DeletePostCommand request,
        CancellationToken cancellationToken)
    {
        var post = await postRepository.GetByIdOrDefaultAsync(request.Id, cancellationToken);

        if (post is null || post.IsDeleted)
        {
            return Result.Failure(PostErrors.NotFoundWithId(request.Id));
        }

        // Soft delete
        post.IsDeleted = true;
        post.DeletedAt = DateTimeOffset.UtcNow;

        postRepository.Update(post);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
