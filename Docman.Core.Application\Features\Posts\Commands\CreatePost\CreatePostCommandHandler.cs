using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Mapster;

namespace Docman.Core.Application.Features.Posts.Commands.CreatePost;

public sealed class CreatePostCommandHandler(
    IGenericRepository<Post> postRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreatePostCommand, Result<CreatePostResponse>>
{
    public async Task<Result<CreatePostResponse>> <PERSON>le(
        CreatePostCommand request,
        CancellationToken cancellationToken)
    {
        var post = request.Adapt<Post>();

        await postRepository.AddAsync(post, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        var response = post.Adapt<CreatePostResponse>();

        return Result.Success(response);
    }
}
