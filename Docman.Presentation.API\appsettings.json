{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=DocmanDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Email": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "From": "<EMAIL>", "Username": "", "Password": ""}, "Storage": {"Provider": "Local", "RootPath": "uploads"}, "ExternalApis": {"Profiles": {"BaseUrl": "https://api.example.com/"}}, "SwaggerConfig": {"EndPoint": "/swagger/v1/swagger.json", "Title": "Docman API", "Version": "v1"}}