using System.Reflection;
using Docman.Core.Application.Common.Behaviors;
using MapsterMapper;
using Microsoft.Extensions.DependencyInjection;

namespace Docman.Core.Application;

public static class ApplicationDependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // MediatR registration
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        
        // FluentValidation registration
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        
        // Pipeline behaviors
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        
        // Mapster registration
        services.AddMapsterServices();

        return services;
    }
    
    private static IServiceCollection AddMapsterServices(this IServiceCollection service)
    {
        var config = TypeAdapterConfig.GlobalSettings;
        config.Scan(Assembly.GetExecutingAssembly());
        service.AddSingleton<IMapper>(new Mapper(config));

        return service;
    }

}
