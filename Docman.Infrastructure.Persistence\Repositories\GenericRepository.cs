using Docman.Infrastructure.Persistence.Context;
using System.Linq.Expressions;
using Docman.Core.Application.Contracts;

namespace Docman.Infrastructure.Persistence.Repositories;

public class GenericRepository<T> : IGenericRepository<T> where T : BaseEntity
{
    protected readonly DocmanDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public GenericRepository(DocmanDbContext context)
    {
        _context = context;
        _dbSet = _context.Set<T>();
    }

    #region Query Methods

    public async Task<List<T>> GetAllAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default)
    {
        var query = BuildQuery(filter, includeProperties, tracked);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<T?> GetAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default)
    {
        var query = BuildQuery(filter, includeProperties, tracked);
        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IQueryable<T>> GetForPaginationAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false)
    {
        return BuildQuery(filter, includeProperties, tracked);
    }

    public async Task<bool> AnyAsync(
        Expression<Func<T, bool>> filter, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => !e.IsDeleted).AnyAsync(filter, cancellationToken);
    }

    #endregion

    #region Command Methods (No SaveChanges - handled by UnitOfWork)

    public async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        entity.CreatedAt = DateTimeOffset.UtcNow;
        await _dbSet.AddAsync(entity, cancellationToken);
        return entity;
    }

    public async Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities), "Entities collection cannot be null.");

        var entitiesList = entities.ToList();
        foreach (var entity in entitiesList)
        {
            entity.CreatedAt = DateTimeOffset.UtcNow;
        }

        await _dbSet.AddRangeAsync(entitiesList, cancellationToken);
    }

    public void Update(T entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        entity.UpdatedAt = DateTimeOffset.UtcNow;
        _dbSet.Update(entity);
    }

    public void UpdateRange(IEnumerable<T> entities)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities), "Entities collection cannot be null.");

        var entitiesList = entities.ToList();
        foreach (var entity in entitiesList)
        {
            entity.UpdatedAt = DateTimeOffset.UtcNow;
        }

        _dbSet.UpdateRange(entitiesList);
    }

    public void Delete(T entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        // Soft delete to match your current pattern
        entity.IsDeleted = true;
        entity.DeletedAt = DateTimeOffset.UtcNow;
        _dbSet.Update(entity);
    }

    public void DeleteRange(IEnumerable<T> entities)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities), "Entities collection cannot be null.");

        var entitiesList = entities.ToList();
        foreach (var entity in entitiesList)
        {
            entity.IsDeleted = true;
            entity.DeletedAt = DateTimeOffset.UtcNow;
        }

        _dbSet.UpdateRange(entitiesList);
    }

    #endregion

    #region Basic CRUD Methods

    public async Task<T> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        var entity = await _dbSet
            .Where(e => !e.IsDeleted)
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        
        if (entity == null)
            throw new KeyNotFoundException($"Entity with id {id} not found");
        
        return entity;
    }

    public async Task<T?> GetByIdOrDefaultAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => !e.IsDeleted)
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
    }

    #endregion

    #region Private Helper Methods

    private IQueryable<T> BuildQuery(
        Expression<Func<T, bool>>? filter,
        string[]? includeProperties,
        bool tracked)
    {
        var query = tracked ? _dbSet : _dbSet.AsNoTracking();

        // Always filter out soft-deleted entities
        query = query.Where(e => !e.IsDeleted);

        if (filter != null)
            query = query.Where(filter);

        if (includeProperties != null && includeProperties.Length > 0)
        {
            foreach (var property in includeProperties)
            {
                if (!string.IsNullOrWhiteSpace(property))
                    query = query.Include(property.Trim());
            }
        }

        return query;
    }

    #endregion
}
