using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Docman.Core.Application.Common.Errors;
using Mapster;

namespace Docman.Core.Application.Features.Posts.Commands.UpdatePost;

public sealed class UpdatePostCommandHandler(
    IGenericRepository<Post> postRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdatePostCommand, Result<UpdatePostResponse>>
{
    public async Task<Result<UpdatePostResponse>> Handle(
        UpdatePostCommand request,
        CancellationToken cancellationToken)
    {
        var post = await postRepository.GetByIdOrDefaultAsync(request.Id, cancellationToken);

        if (post is null || post.IsDeleted)
        {
            return Result.Failure<UpdatePostResponse>(PostErrors.NotFoundWithId(request.Id));
        }

        // Update properties
        post.Title = request.Title;
        post.Content = request.Content;
        
        // Handle publish status change
        if (request.IsPublished && !post.IsPublished)
        {
            post.IsPublished = true;
            post.PublishedAt = DateTimeOffset.UtcNow;
        }
        else if (!request.IsPublished && post.IsPublished)
        {
            post.IsPublished = false;
            post.PublishedAt = null;
        }

        post.UpdatedAt = DateTimeOffset.UtcNow;

        postRepository.Update(post);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        var response = post.Adapt<UpdatePostResponse>();

        return Result.Success(response);
    }
}
