using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Docman.Infrastructure.Persistence.Configurations;

public class PostConfiguration : IEntityTypeConfiguration<Post>
{
    public void Configure(EntityTypeBuilder<Post> builder)
    {
        builder.ToTable("Posts");

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Id)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(p => p.Title)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(p => p.Content)
            .HasMaxLength(10000)
            .IsRequired();

        builder.Property(p => p.UserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(p => p.IsPublished)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(p => p.PublishedAt)
            .IsRequired(false);

        builder.Property(p => p.CreatedAt)
            .IsRequired();

        builder.Property(p => p.UpdatedAt)
            .IsRequired(false);

        builder.Property(p => p.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(p => p.DeletedAt)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(p => p.UserId)
            .HasDatabaseName("IX_Posts_UserId");

        builder.HasIndex(p => p.IsPublished)
            .HasDatabaseName("IX_Posts_IsPublished");

        builder.HasIndex(p => p.CreatedAt)
            .HasDatabaseName("IX_Posts_CreatedAt");

        builder.HasIndex(p => p.IsDeleted)
            .HasDatabaseName("IX_Posts_IsDeleted");

        // Query filter for soft delete
        builder.HasQueryFilter(p => !p.IsDeleted);
    }
}
