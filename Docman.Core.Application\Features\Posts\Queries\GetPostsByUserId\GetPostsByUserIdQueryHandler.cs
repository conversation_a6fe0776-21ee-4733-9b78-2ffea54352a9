using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Docman.Core.Application.Common.DTOs;
using System.Linq.Expressions;
using Mapster;

namespace Docman.Core.Application.Features.Posts.Queries.GetPostsByUserId;

public sealed class GetPostsByUserIdQueryHandler(
    IGenericRepository<Post> postRepository
) : IRequestHandler<GetPostsByUserIdQuery, Result<GetPostsByUserIdResponse>>
{
    public async Task<Result<GetPostsByUserIdResponse>> Handle(
        GetPostsByUserIdQuery request,
        CancellationToken cancellationToken)
    {
        Expression<Func<Post, bool>> filter = p => p.UserId == request.UserId && !p.IsDeleted;

        if (!request.IncludeUnpublished)
        {
            filter = p => p.UserId == request.UserId && !p.IsDeleted && p.IsPublished;
        }

        var posts = await postRepository.GetAllAsync(
            filter: filter,
            tracked: false,
            cancellationToken: cancellationToken);

        var postDtos = posts
            .OrderByDescending(p => p.CreatedAt)
            .Adapt<List<PostDto>>();

        var response = new GetPostsByUserIdResponse(postDtos);

        return Result.Success(response);
    }
}
