using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Docman.Core.Application.Common.Errors;
using Mapster;

namespace Docman.Core.Application.Features.Posts.Queries.GetPostById;

public sealed class GetPostByIdQueryHandler(
    IGenericRepository<Post> postRepository
) : IRequestHandler<GetPostByIdQuery, Result<GetPostByIdResponse>>
{
    public async Task<Result<GetPostByIdResponse>> Handle(
        GetPostByIdQuery request,
        CancellationToken cancellationToken)
    {
        var post = await postRepository.GetByIdOrDefaultAsync(request.Id, cancellationToken);

        if (post is null || post.IsDeleted)
        {
            return Result.Failure<GetPostByIdResponse>(PostErrors.NotFoundWithId(request.Id));
        }

        var response = post.Adapt<GetPostByIdResponse>();

        return Result.Success(response);
    }
}
