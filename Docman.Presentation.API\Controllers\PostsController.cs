using MediatR;
using Microsoft.AspNetCore.Mvc;
using Docman.Presentation.API.Controllers.Base;
using Docman.Presentation.API.Common;
using Docman.Core.Application.Features.Posts.Commands.CreatePost;
using Docman.Core.Application.Features.Posts.Commands.UpdatePost;
using Docman.Core.Application.Features.Posts.Commands.DeletePost;
using Docman.Core.Application.Features.Posts.Queries.GetPostById;
using Docman.Core.Application.Features.Posts.Queries.GetPostsByUserId;
using Docman.Core.Application.Features.Posts.Queries.GetAllPosts;

namespace Docman.Presentation.API.Controllers;

public record UpdatePostPartial(string Title, string Content, bool IsPublished);

public class PostsController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Creates a new post
    /// </summary>
    /// <param name="command">Post creation command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created post</returns>
    [HttpPost]
    [ProducesResponseType(typeof(CreatePostResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreatePost(
        [FromBody] CreatePostCommand command,
        CancellationToken cancellationToken = default)
    {
        var result = await mediator.Send(command, cancellationToken);

        if (result.IsFailure)
            return result.ToActionResult();

        return result.ToActionResult(StatusCodes.Status201Created);
    }

    /// <summary>
    /// Gets a post by ID
    /// </summary>
    /// <param name="id">Post ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Post details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(GetPostByIdResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetPostById(
        string id,
        CancellationToken cancellationToken = default)
    {
        var query = new GetPostByIdQuery(id);
        var result = await mediator.Send(query, cancellationToken);

        if (result.IsFailure)
            return result.ToActionResult(errorStatusCode: StatusCodes.Status404NotFound);

        return Ok(result.Value);
    }

    /// <summary>
    /// Updates an existing post
    /// </summary>
    /// <param name="id">Post ID</param>
    /// <param name="request">Update command (without ID)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated post</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(UpdatePostResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePost(
        string id,
        [FromBody] UpdatePostPartial request,
        CancellationToken cancellationToken = default)
    {
        var command = new UpdatePostCommand(id, request.Title, request.Content, request.IsPublished);
        var result = await mediator.Send(command, cancellationToken);

        if (result.IsFailure)
        {
            var statusCode = result.Error!.Code.Contains("NotFound") 
                ? StatusCodes.Status404NotFound 
                : StatusCodes.Status400BadRequest;
            return result.ToActionResult(errorStatusCode: statusCode);
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// Deletes a post
    /// </summary>
    /// <param name="id">Post ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeletePost(
        string id,
        CancellationToken cancellationToken = default)
    {
        var command = new DeletePostCommand(id);
        var result = await mediator.Send(command, cancellationToken);

        if (result.IsFailure)
            return result.ToActionResult(errorStatusCode: StatusCodes.Status404NotFound);

        return NoContent();
    }

    /// <summary>
    /// Gets all posts by user ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="includeUnpublished">Include unpublished posts</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of posts</returns>
    [HttpGet("user/{userId}")]
    [ProducesResponseType(typeof(GetPostsByUserIdResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetPostsByUserId(
        string userId,
        [FromQuery] bool includeUnpublished = false,
        CancellationToken cancellationToken = default)
    {
        var query = new GetPostsByUserIdQuery(userId, includeUnpublished);
        var result = await mediator.Send(query, cancellationToken);

        if (result.IsFailure)
            return result.ToActionResult();

        return Ok(result.Value);
    }

    /// <summary>
    /// Gets all posts
    /// </summary>
    /// <param name="includeUnpublished">Include unpublished posts</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all posts</returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetAllPostsResponse), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllPosts(
        [FromQuery] bool includeUnpublished = false,
        CancellationToken cancellationToken = default)
    {
        var query = new GetAllPostsQuery(includeUnpublished);
        var result = await mediator.Send(query, cancellationToken);

        if (result.IsFailure)
            return result.ToActionResult();

        return Ok(result.Value);
    }
}
