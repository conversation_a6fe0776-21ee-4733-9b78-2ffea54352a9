using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Entities;
using Docman.Core.Application.Common.DTOs;
using System.Linq.Expressions;
using Mapster;

namespace Docman.Core.Application.Features.Posts.Queries.GetAllPosts;

public sealed class GetAllPostsQueryHandler(
    IGenericRepository<Post> postRepository
) : IRequestHandler<GetAllPostsQuery, Result<GetAllPostsResponse>>
{
    public async Task<Result<GetAllPostsResponse>> Handle(
        GetAllPostsQuery request,
        CancellationToken cancellationToken)
    {
        Expression<Func<Post, bool>> filter = p => !p.IsDeleted;

        if (!request.IncludeUnpublished)
        {
            filter = p => !p.IsDeleted && p.IsPublished;
        }

        var posts = await postRepository.GetAllAsync(
            filter: filter,
            tracked: false,
            cancellationToken: cancellationToken);

        var postDtos = posts
            .OrderByDescending(p => p.CreatedAt)
            .Adapt<List<PostDto>>();

        var response = new GetAllPostsResponse(postDtos);

        return Result.Success(response);
    }
}
