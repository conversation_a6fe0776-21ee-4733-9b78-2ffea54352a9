<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="IIS Express">PlacementTest.WebAPI/PlacementTest.WebAPI.csproj</projectFile>
    <projectFile profileName="http">PlacementTest.WebAPI/PlacementTest.WebAPI.csproj</projectFile>
    <projectFile profileName="https">PlacementTest.WebAPI/PlacementTest.WebAPI.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="068bf04c-2094-4627-b725-94a17ea64e3e" name="Changes" comment="refactor: Simplify PlacementTestContext constructor">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Docman/.idea/.name" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Docman/.idea/.name" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Docman/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Docman/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/.name" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/dictionaries/project.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/encodings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/indexLayout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.PlacementTest/.idea/vcs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.Persistence/Context/PlacementTestContext.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.Persistence/Context/DocmanDbContext.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.Persistence/Repository/BaseRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.Persistence/Repository/BaseRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.Persistence/Repository/TestTakersRepository/TestTakerRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.Persistence/Repository/TestTakersRepository/TestTakerRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.Persistence/Repository/UnitOfWork.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.Persistence/Repository/UnitOfWork.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.Persistence/ServiceExtensions.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.Persistence/ServiceExtensions.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.WebAPI/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.WebAPI/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Docman.WebAPI/appsettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.WebAPI/appsettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlacementTest.sln" beforeDir="false" afterPath="$PROJECT_DIR$/Docman.sln" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;redaelsayied&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/MohanedZekry/WebAPI-CleanArchitecture.git&quot;,
    &quot;accountId&quot;: &quot;8b50de4e-a445-401e-b9a2-18c8af617256&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Docman.WebAPI/Exceptions/GlobalExceptionHandler.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="318YfW9TeqbA11DnVrBRaj4iDlA" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET Launch Settings Profile.PlacementTest.WebAPI: IIS Express.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;
  }
}</component>
  <component name="RunManager" selected=".NET Launch Settings Profile.PlacementTest.WebAPI: IIS Express">
    <configuration name="PlacementTest.WebAPI: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Docman.WebAPI/Docman.WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net7.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="PlacementTest.WebAPI: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Docman.WebAPI/Docman.WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net7.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="PlacementTest.WebAPI: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Docman.WebAPI/Docman.WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net7.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="068bf04c-2094-4627-b725-94a17ea64e3e" name="Changes" comment="" />
      <created>1754907269989</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754907269989</updated>
      <workItem from="1754907271303" duration="8874000" />
      <workItem from="1754917808685" duration="1202000" />
    </task>
    <task id="LOCAL-00001" summary="refactor: Replace AutoMapper with Mapster for object mapping in test taker handlers">
      <option name="closed" value="true" />
      <created>1754909087361</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754909087361</updated>
    </task>
    <task id="LOCAL-00002" summary="chore: Update Swashbuckle.AspNetCore to version 9.0.3">
      <option name="closed" value="true" />
      <created>1754909685699</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754909685700</updated>
    </task>
    <task id="LOCAL-00003" summary="refactor: Organize namespaces and simplify constructor parameters in test taker handlers">
      <option name="closed" value="true" />
      <created>1754910516191</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754910516192</updated>
    </task>
    <task id="LOCAL-00004" summary="refactor: Simplify namespace declarations across multiple files">
      <option name="closed" value="true" />
      <created>1754912129366</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754912129366</updated>
    </task>
    <task id="LOCAL-00005" summary="refactor: Simplify constructor parameters in AddTestTakerHandler and TestTakerController">
      <option name="closed" value="true" />
      <created>1754913112715</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754913112715</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: Implement global exception handling middleware">
      <option name="closed" value="true" />
      <created>1754917490595</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754917490596</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="refactor: Replace AutoMapper with Mapster for object mapping in test taker handlers" />
    <MESSAGE value="chore: Update Swashbuckle.AspNetCore to version 9.0.3" />
    <MESSAGE value="refactor: Organize namespaces and simplify constructor parameters in test taker handlers" />
    <MESSAGE value="refactor: Simplify namespace declarations across multiple files" />
    <MESSAGE value="refactor: Simplify constructor parameters in AddTestTakerHandler and TestTakerController" />
    <MESSAGE value="feat: Implement global exception handling middleware" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: Implement global exception handling middleware" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>