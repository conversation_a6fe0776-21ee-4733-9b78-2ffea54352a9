using System.Reflection;
using Docman.Core.Application.Contracts;
using Docman.Infrastructure.Persistence.Context;
using Docman.Infrastructure.Persistence.Repositories;

namespace Docman.Infrastructure.Persistence;

public static class DependencyInjection
{
    public static IServiceCollection AddPersistenceDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection")
                               ?? throw new InvalidOperationException(
                                   "Connection string 'DefaultConnection' not found.");

        services.AddDbContext<DocmanDbContext>(options =>
        {
            options.UseSqlServer(connectionString,
                b => { b.MigrationsAssembly(Assembly.GetExecutingAssembly().FullName); });
        });

        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));

        return services;
    }
}